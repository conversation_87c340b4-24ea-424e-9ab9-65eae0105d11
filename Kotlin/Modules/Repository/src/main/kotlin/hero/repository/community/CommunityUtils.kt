package hero.repository.community

import hero.model.CommunityMemberStatus
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.util.UUID

fun DSLContext.fetchCommunityIds(userId: String): Set<UUID> {
    return this
        .select(Tables.COMMUNITY_MEMBER.COMMUNITY_ID)
        .from(Tables.COMMUNITY_MEMBER)
        .where(Tables.COMMUNITY_MEMBER.USER_ID.eq(userId))
        .and(Tables.COMMUNITY_MEMBER.STATE.eq(CommunityMemberStatus.ACTIVE.name))
        .fetch()
        .map { it.value1() }
        .toSet()
}
