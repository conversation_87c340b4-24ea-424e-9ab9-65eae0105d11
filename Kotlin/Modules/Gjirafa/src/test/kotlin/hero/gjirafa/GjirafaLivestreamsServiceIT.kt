package hero.gjirafa

import hero.baseutils.FuelException
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.plusDays
import hero.gjirafa.dto.PostLiveVideoRequest
import hero.gjirafa.dto.PutLiveVideoRequest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class GjirafaLivestreamsServiceIT {
    private val projectId = SystemEnv.gjirafaProject
    private val service = GjirafaLivestreamsService(projectId, SystemEnv.gjirafaApiKey)

    @Test
    fun `create and delete channel and the whole livestream flow`() {
        val userId = "test_user_" + UUID.randomUUID()
        val stamp = Instant.now().plusDays(1).truncatedTo(ChronoUnit.MINUTES)
        val channel = service.postChannel(userId, userId, "https://picsum.photos/id/0/600/400")
        try {
            log.info("Testing with channel ${channel.publicId}")
            val videoRequest = PostLiveVideoRequest(
                title = "title-$userId",
                author = userId,
                description = "description-$userId",
                channelId = channel.publicId,
                dvrEnabled = true,
                rewindDuration = 30,
                rewindTypeId = 1,
                publishDate = stamp,
                publishEndDate = stamp.plusDays(1),
            )
            val video = try {
                service.postLiveVideo(videoRequest)
            } catch (e: Exception) {
                // if we don't display this exception, the message may not be printed if the 'finally' block fails
                log.error(e.message, cause = e.cause)
                throw e
            }
            try {
                log.info("Testing with video ${video.id}")
                assertEquals(videoRequest.title, video.title)
                assertEquals(videoRequest.description, video.description)
                assertEquals(stamp, video.publishDate?.atZone(ZoneOffset.UTC)?.toInstant())
                assertEquals(stamp.plusDays(1), video.publishEndDate?.atZone(ZoneOffset.UTC)?.toInstant())

                service.assertAuthor(userId, video.id)

                val patchedVideo = service.putLiveVideo(
                    video.id,
                    PutLiveVideoRequest(
                        title = "title2-$userId",
                        author = userId,
                        description = "description2-$userId",
                        publishDate = stamp.plusDays(3),
                        publishEndDate = stamp.plusDays(4),
                    ),
                )
                assertTrue("title2" in patchedVideo.title)
                assertTrue("description2" in patchedVideo.description!!)
                assertEquals(stamp.plusDays(3), patchedVideo.publishDate?.atZone(ZoneOffset.UTC)?.toInstant())
                assertEquals(stamp.plusDays(4), patchedVideo.publishEndDate?.atZone(ZoneOffset.UTC)?.toInstant())

                val gotVideo = service.getLiveVideo(video.id)
                assertTrue("title2" in patchedVideo.title)
                assertTrue("description2" in patchedVideo.description)
                assertEquals(stamp.plusDays(3), patchedVideo.publishDate?.atZone(ZoneOffset.UTC)?.toInstant())
                assertEquals(stamp.plusDays(4), patchedVideo.publishEndDate?.atZone(ZoneOffset.UTC)?.toInstant())

                val response = service.postVideoThumbnail(video.id, "https://picsum.photos/id/0/600/400")
                assertTrue("https://cdn.vpplayer.tech/$projectId/" in response.path)
                // "going live" throws exception because stream must be connected to the streamer
                val exception = assertThrows<FuelException> { service.goLive(video.id) }
                assertTrue("Please double-check that the channel is fully prepared" in exception.message)
            } finally {
                service.deleteLiveVideo(video.id)
            }
        } finally {
            service.deleteChannel(channel.publicId)
        }
    }
}
