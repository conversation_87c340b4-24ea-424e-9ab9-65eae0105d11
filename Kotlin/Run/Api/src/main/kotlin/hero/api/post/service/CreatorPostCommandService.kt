package hero.api.post.service

import hero.api.post.service.dto.PostInput
import hero.baseutils.log
import hero.baseutils.minus
import hero.core.logging.Logger
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.model.Category
import hero.model.Post
import hero.repository.community.fetchCommunityIds
import org.jooq.DSLContext
import java.time.Instant
import java.util.UUID
import kotlin.time.Duration.Companion.minutes

class CreatorPostCommandService(
    lazyContext: Lazy<DSLContext>,
    private val postService: PostService,
    private val categoriesCollection: TypedCollectionReference<Category>,
    private val logger: Logger = log,
) {
    private val context by lazyContext

    fun execute(command: CreateCreatorPost): Post {
        if (command.publishedAt?.isBefore(Instant.now() - 5.minutes) == true) {
            throw BadRequestException("Field publishedAt can be at most 5 minutes in the past")
        }
        validateCategories(command.categories, command.creatorId)

        if (command.communityId != null) {
            val communityIds = context.fetchCommunityIds(command.creatorId)
            if (command.communityId !in communityIds) {
                throw ForbiddenException(
                    "User ${command.creatorId} cannot post in community ${command.communityId}",
                )
            }
        }

        val post = postService.execute(
            CreatePost(
                userId = command.creatorId,
                publishedAt = command.publishedAt ?: Instant.now(),
                categories = command.categories,
                text = command.attributes.text,
                textHtml = command.attributes.textHtml,
                assets = command.attributes.assets,
                parentUserId = command.creatorId,
                textDelta = command.attributes.textDelta,
                isAgeRestricted = command.isAgeRestricted,
                isSponsored = command.isSponsored,
                communityId = command.communityId,
            ),
        )
        logger.info("Creator ${command.creatorId} created a post ${post.id}", post.toLabels())

        return post
    }

    fun execute(command: UpdateCreatorPost): Post {
        if (command.publishedAt?.isBefore(Instant.now() - 5.minutes) == true) {
            throw BadRequestException("Field publishedAt can be at most 5 minutes in the past")
        }
        validateCategories(command.categories, command.creatorId)

        val post = postService.execute(
            UpdatePost(
                userId = command.creatorId,
                postId = command.postId,
                publishedAt = command.publishedAt,
                pinnedAt = command.pinnedAt,
                categories = command.categories.toList(),
                text = command.attributes.text,
                textDelta = command.attributes.textDelta,
                textHtml = command.attributes.textHtml,
                assets = command.attributes.assets,
                isAgeRestricted = command.isAgeRestricted,
                isSponsored = command.isSponsored,
                excludeFromRss = command.excludeFromRss,
            ),
        )
        logger.info("Creator ${command.creatorId} updated a post ${post.id}", post.toLabels())

        return post
    }

    private fun validateCategories(
        categories: Set<String>,
        userId: String,
    ) {
        if (categories.isEmpty()) {
            return
        }

        val userCategories = categoriesCollection
            .where(Category::userId).isEqualTo(userId)
            .fetchAll()
            .map { it.id }
            .toSet()

        val invalidCategories = categories - userCategories
        if (invalidCategories.isNotEmpty()) {
            throw BadRequestException(
                "Invalid categories $invalidCategories, creator '$userId' does not have these categories",
            )
        }
    }
}

data class CreateCreatorPost(
    val creatorId: String,
    val attributes: PostInput,
    val categories: Set<String>,
    val isAgeRestricted: Boolean,
    val isSponsored: Boolean,
    val communityId: UUID?,
    val publishedAt: Instant? = null,
)

data class UpdateCreatorPost(
    val creatorId: String,
    val postId: String,
    val attributes: PostInput,
    val categories: Set<String>,
    val isAgeRestricted: Boolean,
    val isSponsored: Boolean,
    val excludeFromRss: Boolean,
    val publishedAt: Instant? = null,
    val pinnedAt: Instant? = null,
)
