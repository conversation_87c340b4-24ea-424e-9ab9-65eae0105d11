package hero.api.post.controller.dto

import hero.api.post.service.CommentBaseData
import hero.api.post.service.CommentData
import hero.api.post.service.CommentWithSubscriptionInfo
import hero.api.post.service.CreatorPostWithMeta
import hero.api.post.service.ReplyData
import hero.api.post.service.SavedCreatorPostInfo
import hero.api.post.service.toDto
import hero.contract.api.dto.CategoryResponse
import hero.contract.api.dto.PostRelationships
import hero.contract.api.dto.PostResponse
import hero.contract.api.dto.SavedCreatorPostInfoResponse
import hero.model.Category
import hero.model.Post
import hero.model.PostRenderMeta
import hero.model.Subscriber
import hero.model.SubscriberStatus
import java.time.Instant

fun Post.toResponse(
    renderMeta: PostRenderMeta,
    categories: List<Category> = listOf(),
    savedCreatorPostInfo: SavedCreatorPostInfo? = null,
): PostResponse =
    PostResponse(
        id = id,
        publishedAt = published,
        pinnedAt = pinnedAt,
        state = state,
        text = if (renderMeta.showText) text.trim() else null,
        textHtml = if (renderMeta.showText) textHtml?.trim() else null,
        textDelta = if (renderMeta.showText) textDelta?.trim() else null,
        fullAsset = renderMeta.fullResponse,
        counts = counts,
        excludeFromRss = if (renderMeta.isAuthor) excludeFromRss else null,
        assets = when {
            assets.isNotEmpty() -> assets.filter { !it.isEmpty() }.map { it.toDto(renderMeta) }
            else -> listOf()
        },
        price = price,
        assetsCount = assets.size,
        categories = categories.map { CategoryResponse(id = it.id, name = it.name, slug = it.slug) },
        savedPostInfo = savedCreatorPostInfo?.let { SavedCreatorPostInfoResponse(it.id, it.savedAt) },
        relationships = PostRelationships(
            userId = userId,
            parentId = parentId,
            siblingId = siblingId,
            messageThreadId = messageThreadId,
        ),
        chapters = chapters ?: emptyList(),
        isAgeRestricted = isAgeRestricted,
        isSponsored = isSponsored,
        pollId = pollId,
    )

fun CreatorPostWithMeta.toResponse(requesterId: String?): PostResponse {
    val isPostAuthor = post.userId == requesterId
    val fullResponse = isPostAuthor ||
        (post.communityId != null && isPartOfCommunity == true) ||
        // user cannot view community post even he subscribes the post author
        (post.communityId == null && isValidSubscription(requesterId, post.userId, subscriptionInfo))

    return post.toResponse(
        PostRenderMeta(fullResponse, showText = fullResponse, isAuthor = isPostAuthor),
        categories,
        savedPostInfo,
    )
}

fun CommentWithSubscriptionInfo.toResponse(requesterId: String?): PostResponse {
    val isPostAuthor = postAuthor == requesterId
    val fullResponse = isPostAuthor || isValidSubscription(requesterId, postAuthor, subscriptionInfo)

    return comment.toResponse(PostRenderMeta(fullResponse, showText = fullResponse))
}

fun CommentBaseData.toResponse(requesterId: String): CommentResponse {
    val isPostAuthor = rootPost.userId == requesterId
    val fullResponse = isPostAuthor || isValidSubscription(requesterId, rootPost.userId, subscriptionInfo)

    return when (this) {
        is CommentData -> {
            val parent = rootPost.toResponse(PostRenderMeta(fullResponse))
            CommentResponse(
                comment = comment.toResponse(PostRenderMeta(true)),
                parent = parent,
                rootParent = parent,
            )
        }

        is ReplyData -> CommentResponse(
            comment = comment.toResponse(PostRenderMeta(true)),
            parent = parent.toResponse(PostRenderMeta(true)),
            rootParent = rootPost.toResponse(PostRenderMeta(fullResponse)),
        )
    }
}

private fun isValidSubscription(
    requesterId: String?,
    creatorId: String,
    sub: Subscriber?,
): Boolean {
    if (sub == null || requesterId == null) {
        return false
    }
    if (requesterId != sub.userId) {
        error("Requester id [$requesterId] is different than fetched subscriber user id [${sub.userId}]")
    }
    if (creatorId != sub.creatorId) {
        error("Post creator id [$creatorId] is different than fetched subscriber creator id [${sub.creatorId}]")
    }

    val expires = sub.expires
    val isNotExpired = expires == null || expires.isAfter(Instant.now())
    return isNotExpired && sub.status in SubscriberStatus.activeStatuses
}
