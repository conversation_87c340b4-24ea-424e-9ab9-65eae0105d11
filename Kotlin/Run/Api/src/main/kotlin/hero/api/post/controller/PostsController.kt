package hero.api.post.controller

import hero.api.post.controller.dto.CreateCommentRequest
import hero.api.post.controller.dto.CreatePostRequest
import hero.api.post.controller.dto.PostVoteRequest
import hero.api.post.controller.dto.SearchPostsRequest
import hero.api.post.controller.dto.UpdateCommentRequest
import hero.api.post.controller.dto.UpdatePostRequest
import hero.api.post.controller.dto.exampleCommentResponse
import hero.api.post.controller.dto.exampleCreateCommentRequest
import hero.api.post.controller.dto.exampleCreatePostRequest
import hero.api.post.controller.dto.examplePaginatedPostResponse
import hero.api.post.controller.dto.examplePostResponse
import hero.api.post.controller.dto.examplePostVoteRequest
import hero.api.post.controller.dto.exampleSearchPostsRequest
import hero.api.post.controller.dto.exampleUpdateCommentRequest
import hero.api.post.controller.dto.exampleUpdatePostRequest
import hero.api.post.controller.dto.toResponse
import hero.api.post.service.CastPostVote
import hero.api.post.service.CommentCommandService
import hero.api.post.service.CommentQueryService
import hero.api.post.service.CreateComment
import hero.api.post.service.CreateCreatorPost
import hero.api.post.service.CreatorPostCommandService
import hero.api.post.service.CreatorPostQueryService
import hero.api.post.service.CreatorPostsSortingFields
import hero.api.post.service.DeleteCreatorPost
import hero.api.post.service.DeletePostCommandService
import hero.api.post.service.GetComment
import hero.api.post.service.GetComments
import hero.api.post.service.GetCreatorLivestreams
import hero.api.post.service.GetCreatorPostDetails
import hero.api.post.service.GetCreatorPosts
import hero.api.post.service.GetCreatorPostsFilter
import hero.api.post.service.PostVoteCommandService
import hero.api.post.service.UpdateComment
import hero.api.post.service.UpdateCreatorPost
import hero.core.data.PageRequest
import hero.core.data.Sort
import hero.core.data.toResponse
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.parseJwtUser
import hero.http4k.config.PAGE_SIZE_DEFAULT
import hero.http4k.controller.QueryUtils
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.body
import hero.http4k.extensions.delete
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.put
import hero.model.PostRenderMeta
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.regex
import org.http4k.lens.string

class PostsController(
    private val postVoteCommandService: PostVoteCommandService,
    private val deletePostCommandService: DeletePostCommandService,
    private val creatorPostQueryService: CreatorPostQueryService,
    private val creatorPostCommandService: CreatorPostCommandService,
    private val commentQueryService: CommentQueryService,
    private val commentCommandService: CommentCommandService,
) {
    @Suppress("unused")
    val routeCreatePost: ContractRoute =
        ("/v4/posts").post(
            summary = "Create a post",
            tag = "Posts",
            parameters = object {},
            receiving = exampleCreatePostRequest,
            responses = listOf(Status.OK example examplePostResponse),
            handler = { request, _ ->
                val user = request.getJwtUser()
                val body = lens<CreatePostRequest>(request)
                val result = creatorPostCommandService.execute(
                    CreateCreatorPost(
                        creatorId = user.id,
                        attributes = body.attributes,
                        categories = body.categories,
                        publishedAt = body.publishedAt,
                        isAgeRestricted = body.isAgeRestricted,
                        communityId = body.communityId,
                        isSponsored = body.isSponsored,
                    ),
                ).let {
                    creatorPostQueryService.execute(GetCreatorPostDetails(user.id, it.id))
                }

                Response(Status.OK).body(result.toResponse(user.id))
            },
        )

    @Suppress("unused")
    val routeUpdatePost: ContractRoute =
        ("/v4/posts" / Path.string().of("postId")).put(
            summary = "Update a post with given id",
            tag = "Posts",
            parameters = object {},
            receiving = exampleUpdatePostRequest,
            responses = listOf(Status.OK example examplePostResponse),
            handler = { request, _, postId ->
                val user = request.getJwtUser()
                val body = lens<UpdatePostRequest>(request)
                val result = creatorPostCommandService.execute(
                    UpdateCreatorPost(
                        creatorId = user.id,
                        postId = postId,
                        attributes = body.attributes,
                        categories = body.categories,
                        publishedAt = body.publishedAt,
                        pinnedAt = body.pinnedAt,
                        isAgeRestricted = body.isAgeRestricted,
                        isSponsored = body.isSponsored,
                        excludeFromRss = body.excludeFromRss,
                    ),
                ).let {
                    creatorPostQueryService.execute(GetCreatorPostDetails(user.id, it.id))
                }

                Response(Status.OK).body(result.toResponse(user.id))
            },
        )

    @Suppress("unused")
    val routeDeletePost: ContractRoute =
        ("/v4/posts" / Path.string().of("postId")).delete(
            summary = "Update a post with given id",
            tag = "Posts",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT to Unit),
            handler = { request, _, postId ->
                val user = request.getJwtUser()

                deletePostCommandService.execute(DeleteCreatorPost(userId = user.id, postId = postId))

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    val routeGetPost: ContractRoute =
        ("/v4/posts" / Path.string().of("postId")).get(
            summary = "Get post by Id.",
            tag = "Posts",
            parameters = object {},
            responses = listOf(Status.OK example examplePostResponse),
            handler = { request, _, postId ->
                val userId = request.parseJwtUser()?.id
                val result = creatorPostQueryService.execute(GetCreatorPostDetails(userId, postId))

                Response(Status.OK).body(result.toResponse(userId))
            },
        )

    @Suppress("unused")
    val routeGetComments: ContractRoute =
        ("/v4/posts" / Path.string().of("postId") / "comments").get(
            summary = "Get comments for a post or comment",
            tag = "Posts",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
                val beforeCursor = QueryUtils.beforeCursor()
                val sort = QueryUtils.sortDirection()
            },
            responses = listOf(Status.OK example examplePaginatedPostResponse),
            handler = { request, parameters, postId, _ ->
                val pageSize = parameters.pageSize(request)
                val afterCursor = parameters.afterCursor(request)
                val beforeCursor = parameters.beforeCursor(request)
                val userId = request.parseJwtUser()?.id
                val sort = parameters.sort(request) ?: Sort.Direction.DESC

                val pageable = PageRequest(
                    pageSize = pageSize,
                    afterCursor = afterCursor,
                    beforeCursor = beforeCursor,
                    sort = Sort(direction = sort),
                )
                val result = commentQueryService.execute(GetComments(userId, postId, pageable))
                val response = result.toResponse { it.toResponse(userId) }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("unused")
    val routeGetSingleComment: ContractRoute =
        ("/v1/comments" / Path.string().of("commentId")).get(
            summary = "Get single comment",
            tag = "Posts",
            parameters = object {},
            responses = listOf(Status.OK example exampleCommentResponse),
            handler = { request, _, commentId ->
                val user = request.getJwtUser()

                val result = commentQueryService.execute(GetComment(commentId, user.id))

                Response(Status.OK).body(result.toResponse(user.id))
            },
        )

    @Suppress("unused")
    val routeCreateComment: ContractRoute =
        ("/v1/comments").post(
            summary = "Create comment",
            tag = "Posts",
            parameters = object {},
            responses = listOf(Status.OK example exampleCommentResponse),
            receiving = exampleCreateCommentRequest,
            handler = { request, _ ->
                val user = request.getJwtUser()
                val body = lens<CreateCommentRequest>(request)

                val comment = commentCommandService.execute(
                    CreateComment(
                        userId = user.id,
                        parentId = body.parentId,
                        siblingId = body.siblingId,
                        attributes = body.attributes,
                    ),
                )

                Response(Status.OK).body(comment.toResponse(PostRenderMeta(fullResponse = true)))
            },
        )

    @Suppress("unused")
    val routeUpdateComment: ContractRoute =
        ("/v1/comments" / Path.string().of("commentId")).put(
            summary = "Update a comment",
            tag = "Posts",
            parameters = object {},
            responses = listOf(Status.OK example exampleCommentResponse),
            receiving = exampleUpdateCommentRequest,
            handler = { request, _, commentId ->
                val user = request.getJwtUser()
                val body = lens<UpdateCommentRequest>(request)

                val comment = commentCommandService.execute(
                    UpdateComment(
                        userId = user.id,
                        commentId = commentId,
                        attributes = body.attributes,
                    ),
                )

                Response(Status.OK).body(comment.toResponse(PostRenderMeta(fullResponse = true)))
            },
        )

    @Suppress("unused")
    val routeCreatorPosts: ContractRoute =
        ("/v1/users" / Path.userId().of("creatorId") / "posts").get(
            summary = "Get creator's posts",
            tag = "Posts",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
                val sortDirection = QueryUtils.sortDirection()
                val sortBy = Query.enum<CreatorPostsSortingFields>().optional(
                    "by",
                    "Field to sort by, default is pinned_at",
                )
                val categoryId = Query.regex("([a-z0-9|-]+)").optional("categoryId", "Filter posts by categoryId.")
            },
            responses = listOf(Status.OK example examplePaginatedPostResponse),
            handler = { request, parameters, creatorId, _ ->
                val pageSize = parameters.pageSize(request)
                val afterCursor = parameters.afterCursor(request)
                val categoryId = parameters.categoryId(request)
                val sortDirection = parameters.sortDirection(request)
                val sortBy = parameters.sortBy(request)
                val userId = request.parseJwtUser()?.id

                val sort = Sort(by = sortBy?.name, sortDirection ?: Sort.Direction.DESC)
                val pageable = PageRequest(pageSize = pageSize, afterCursor = afterCursor, sort = sort)
                val filter = GetCreatorPostsFilter(categoryId = categoryId)
                val result = creatorPostQueryService.execute(GetCreatorPosts(userId, creatorId, pageable, filter))
                    .let {
                        val (livestreams, other) = it.content.partition { item -> item.post.isLivestreamLive() }
                        it.copy(content = livestreams + other)
                    }

                val response = result.toResponse { it.toResponse(userId) }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("unused")
    val routeCreatorPostsSearch: ContractRoute =
        ("/v3/posts/search").post(
            summary = "Search posts",
            tag = "Posts",
            parameters = object {},
            responses = listOf(Status.OK example examplePaginatedPostResponse),
            receiving = exampleSearchPostsRequest,
            handler = { request, _ ->
                val body = lens<SearchPostsRequest>(request)
                val userId = request.parseJwtUser()?.id

                val sort = Sort(by = body.sortBy?.name, body.sortDirection ?: Sort.Direction.DESC)
                val pageable = PageRequest(
                    pageSize = body.pageSize ?: PAGE_SIZE_DEFAULT,
                    afterCursor = body.afterCursor,
                    beforeCursor = body.beforeCursor,
                    sort = sort,
                )
                val result =
                    creatorPostQueryService.execute(GetCreatorPosts(userId, body.creatorId, pageable, body.filter))
                        .let {
                            val (livestreams, other) = it.content.partition { item -> item.post.isLivestreamLive() }
                            it.copy(content = livestreams + other)
                        }

                val response = result.toResponse { it.toResponse(userId) }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("unused")
    val routeCreatorLivestreams: ContractRoute =
        ("/v1/users" / Path.userId().of("creatorId") / "livestreams").get(
            summary = "Get creator's livestreams",
            tag = "Posts",
            parameters = object {},
            responses = listOf(Status.OK example examplePaginatedPostResponse),
            handler = { request, _, creatorId, _ ->
                val userId = request.getJwtUser().id
                if (creatorId != userId) {
                    throw ForbiddenException("Livestreams are accessible only for the owner right now")
                }

                val result = creatorPostQueryService.execute(GetCreatorLivestreams(creatorId))

                val response = result.toResponse { it.toResponse(PostRenderMeta(fullResponse = true)) }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("unused")
    val routePostPostVote: ContractRoute =
        ("/v4/posts" / Path.string().of("postId") / "votes").post(
            summary = "Vote on given post",
            tag = "Posts",
            parameters = object {},
            receiving = examplePostVoteRequest,
            responses = listOf(Status.NO_CONTENT to Unit),
            handler = { request, _, postId, _ ->
                val user = request.getJwtUser()
                val body = lens<PostVoteRequest>(request)
                postVoteCommandService.execute(CastPostVote(user.id, postId, body.voteValue))

                Response(Status.NO_CONTENT)
            },
        )
}
