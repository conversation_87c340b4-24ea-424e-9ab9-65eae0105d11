package hero.api.post.service

import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.jooq.DSLContext
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.time.Clock
import java.time.Instant

class PostVoteCommandServiceIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(**********)

    @ParameterizedTest
    @CsvSource(
        value = [
            "1",
            "0",
            "-1",
        ],
    )
    fun `should create a post vote and change the vote score to given value`(voteValue: Int) {
        val underTest = prepareService()
        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir")
        testHelper.createCommunityMember(community.id, "cestmir")
        testHelper.createPost("cestmir", communityId = community.id, id = "thread-id")

        underTest.execute(CastPostVote("cestmir", "thread-id", voteValue))

        val post = TestCollections.postsCollection["thread-id"].get()
        assertThat(post.voteScore).isEqualTo(voteValue.toLong())

        with(testContext.selectFrom(Tables.POST_VOTE).fetchSingle()) {
            assertThat(this.postId).isEqualTo("thread-id")
            assertThat(this.userId).isEqualTo("cestmir")
            assertThat(this.voteValue).isEqualTo(voteValue)
            assertThat(this.votedAt).isEqualTo(expectedTimestamp)
            assertThat(this.createdAt).isEqualTo(expectedTimestamp)
            assertThat(this.updatedAt).isEqualTo(expectedTimestamp)
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "1, 1, 0",
            "1, 0, -1",
            "1, -1, -2",

            "0, 1, 1",
            "0, 0, 0",
            "0, -1, -1",

            "-1, 1, 2",
            "-1, 0, 1",
            "-1, -1, 0",
        ],
    )
    fun `should create a post vote and change the vote score to given value`(
        oldValue: Int,
        newValue: Int,
        expectedValue: Int,
    ) {
        val underTest = prepareService()
        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir")
        testHelper.createCommunityMember(community.id, "cestmir")
        testHelper.createPost("cestmir", communityId = community.id, id = "thread-id")
        testContext.createPostVote("cestmir", "thread-id", oldValue)

        underTest.execute(CastPostVote("cestmir", "thread-id", newValue))

        val post = TestCollections.postsCollection["thread-id"].get()
        assertThat(post.voteScore).isEqualTo(expectedValue.toLong())

        with(testContext.selectFrom(Tables.POST_VOTE).fetchSingle()) {
            assertThat(this.postId).isEqualTo("thread-id")
            assertThat(this.userId).isEqualTo("cestmir")
            assertThat(this.voteValue).isEqualTo(voteValue)
            assertThat(this.votedAt).isEqualTo(expectedTimestamp)
            assertThat(this.createdAt).isNotEqualTo(expectedTimestamp)
            assertThat(this.updatedAt).isEqualTo(expectedTimestamp)
        }
    }

    private fun DSLContext.createPostVote(
        userId: String,
        postId: String,
        voteValue: Int,
    ) {
        insertInto(Tables.POST_VOTE)
            .set(Tables.POST_VOTE.USER_ID, userId)
            .set(Tables.POST_VOTE.POST_ID, postId)
            .set(Tables.POST_VOTE.VOTE_VALUE, voteValue)
            .set(Tables.POST_VOTE.CREATED_AT, Instant.now())
            .set(Tables.POST_VOTE.UPDATED_AT, Instant.now())
            .set(Tables.POST_VOTE.VOTED_AT, Instant.now())
            .execute()
    }

    private fun prepareService(testClock: Clock = TestClock(expectedTimestamp)): PostVoteCommandService {
        return PostVoteCommandService(
            lazyTestContext,
            TestRepositories.postRepository,
            TestCollections.postsCollection,
            testClock,
        )
    }
}
