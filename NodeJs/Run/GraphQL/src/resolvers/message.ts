import { MessageThreadResolvers } from '../generated/resolvers-types'

export const messageThreadResolvers: MessageThreadResolvers = {
    participants: async ({ participants, participantIds }, _, { dataSources }) => {
        if (participants.length != 0) {
            return participants
        }

        return await Promise.all(participantIds.map((id) => dataSources.userAPI.getUser(id)))
    },
}
